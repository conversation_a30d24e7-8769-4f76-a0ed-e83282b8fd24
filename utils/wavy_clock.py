import cairo
import datetime
import math
from gi.repository import Gtk, GLib, Gdk
import gi

gi.require_version("Gtk", "3.0")


class WavyCircle(Gtk.DrawingArea):
    def __init__(self):
        super().__init__()
        self.set_name("wavy-circle")  # Set CSS name for styling
        self.get_style_context().add_class("wavy-circle")  # Add CSS class
        self.connect("draw", self.on_draw)
        self.set_size_request(-1, 153)

        GLib.timeout_add_seconds(1, self.on_tick)

        self.show()

    def on_tick(self):
        self.queue_draw()
        return True

    def on_draw(self, widget, ctx):
        width = self.get_allocated_width()
        height = self.get_allocated_height()
        cx, cy = width / 2, height / 2

        base_radius = min(width, height) * 0.4
        amplitude = base_radius * 0.05
        frequency = 10

        # Get colors from CSS styling
        style = self.get_style_context()

        # Try to get colors from CSS properties, with fallbacks
        primary_color = Gdk.RGBA()
        secondary_color = Gdk.RGBA()
        accent_color = Gdk.RGBA()

        # Get primary color (for outer wavy circle)
        if style.lookup_color("color", primary_color):
            pass  # Successfully got color from CSS
        else:
            primary_color = Gdk.RGBA(0.29, 0.56, 0.89, 1.0)  # Blue fallback

        # Get secondary color (for hour/minute hands)
        if style.lookup_color("background-color", secondary_color):
            pass  # Successfully got background-color from CSS
        else:
            secondary_color = Gdk.RGBA(0.17, 0.24, 0.31, 1.0)  # Dark fallback

        # Get accent color (for second hand dot)
        if style.lookup_color("border-color", accent_color):
            pass  # Successfully got border-color from CSS
        else:
            accent_color = Gdk.RGBA(0.91, 0.3, 0.24, 1.0)  # Red fallback

        # wavy outer circle
        ctx.set_line_width(4)
        angle_step = 2 * math.pi / 500
        ctx.move_to(
            cx + (base_radius + amplitude * math.sin(frequency * 0)) * math.cos(0),
            cy + (base_radius + amplitude * math.sin(frequency * 0)) * math.sin(0),
        )

        angle = 0
        while angle <= math.tau:
            r = base_radius + amplitude * math.sin(frequency * angle)
            x = cx + r * math.cos(angle)
            y = cy + r * math.sin(angle)
            ctx.line_to(x, y)
            angle += angle_step

        ctx.set_source_rgba(
            primary_color.red,
            primary_color.green,
            primary_color.blue,
            primary_color.alpha,
        )
        ctx.close_path()
        ctx.fill_preserve()
        ctx.set_source_rgba(
            primary_color.red,
            primary_color.green,
            primary_color.blue,
            primary_color.alpha,
        )
        ctx.stroke()

        ANGLE_OFFSET = 0.25
        now = datetime.datetime.now()
        seconds = now.second + now.microsecond / 1e6
        hour = now.hour % 12 + now.minute / 60.0
        minute = now.minute + now.second / 60.0

        second_angle = (seconds / 60.0 - ANGLE_OFFSET) * math.tau
        hour_angle = (hour / 12.0 - ANGLE_OFFSET) * math.tau
        minute_angle = (minute / 60.0 - ANGLE_OFFSET) * math.tau

        hour_orbit = base_radius * 0.8 - 28
        minute_orbit = base_radius * 0.8 - 14
        second_orbit = base_radius * 0.8
        dot_radius = 9

        ctx.set_line_cap(cairo.LINE_CAP_ROUND)

        # hour hand
        ctx.set_line_width(6)  # Reduced width for better visibility
        ctx.set_source_rgba(
            secondary_color.red, secondary_color.green, secondary_color.blue, 1.0
        )
        ctx.move_to(cx, cy)
        ctx.line_to(
            cx + hour_orbit * math.cos(hour_angle),
            cy + hour_orbit * math.sin(hour_angle),
        )
        ctx.stroke()

        # minute hand
        ctx.set_line_width(4)  # Reduced width for better visibility
        ctx.set_source_rgba(
            secondary_color.red, secondary_color.green, secondary_color.blue, 1.0
        )
        ctx.move_to(cx, cy)
        ctx.line_to(
            cx + minute_orbit * math.cos(minute_angle),
            cy + minute_orbit * math.sin(minute_angle),
        )
        ctx.stroke()

        # second dot
        x = cx + second_orbit * math.cos(second_angle)
        y = cy + second_orbit * math.sin(second_angle)
        ctx.arc(x, y, dot_radius, 0, math.tau)
        ctx.set_source_rgba(
            accent_color.red, accent_color.green, accent_color.blue, 1.0
        )
        ctx.fill()

        # center dot to anchor the hands
        ctx.arc(cx, cy, 4, 0, math.tau)
        ctx.set_source_rgba(
            secondary_color.red, secondary_color.green, secondary_color.blue, 1.0
        )
        ctx.fill()
