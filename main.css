@import url("./styles/dock.css");
@import url("./styles/colors.css");
@import url("./styles/components.css");
@import url("./styles/controls.css");
@import url("./styles/indicators.css");
@import url("./styles/workspaces.css");
@import url("./styles/switcher.css");
@import url("./styles/osd.css");
@import url("./styles/launcher.css");
@import url("./styles/kanban.css");
@import url("./styles/calendar.css");
@import url("./styles/notification.css");
@import url("./styles/player.css");

* {
  all: unset;
  color: var(--foreground);
  font-size: unset;
  font-family: unset;
  border-radius: 16px;
}

#corner {
  background-color: var(--shadow);
  border-radius: 0;
}

#corner-container {
  min-width: 20px;
  min-height: 20px;
}

tooltip {
  border: solid 1px;
  border-color: var(--surface);
  background-color: var(--shadow);
  animation: tooltipShow 0.25s cubic-bezier(0.5, 0.25, 0, 1);
}

@keyframes tooltipShow {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

tooltip > * {
  padding: 6px 10px;
  border-radius: 10px;
}

#workspaces,
#workspaces-container,
#launcher,
#date-time,
#music-player,
#osd,
#tray,
#battery,
#controls,
#notification-stack-box,
#metrics,
#language,
#applications-dock,
#dock {
  box-shadow: 0 0 3px alpha(black, 0.7);
}

#dashboard.hide {
  margin: -999px;
  transition: margin 0.25s cubic-bezier(0.5, 0.25, 0, 1);
}
#dashboard.reveal {
  margin: 0px;
  transition: margin 0.25s cubic-bezier(0.5, 0.25, 0, 1);
}
#dashboard {
  background-color: black;
  border-radius: 25px;
  padding: 10px;
}
#inner {
  margin: 3px;
}

.tile {
  margin: 2px;
  min-height: 60px;
  min-width: 140px;
  border-radius: 20px;
}
.tile.mini {
  min-height: 46px;
  min-width: 46px;
  border-radius: 100%;
}
.tile.on {
  background-color: var(--primary);
}
.tile.off {
  background-color: var(--surface-bright);
}
.tile.on .desc-label,
.tile.on .tile-label,
.tile.on .tile-icon {
  color: black;
  transition: all;
}
.tile.off .desc-label,
.tile.off .tile-label,
.tile.off .tile-icon {
  color: var(--foreground);
  transition: all;
}

.tile-icon {
  font-size: 16px;
  margin: 10px;
}
.tile-icon.mini {
  font-size: 20px;
  transition: font-size 0.25s cubic-bezier(0.5, 0.25, 0, 1);
}
.tile-icon.maxi {
  font-size: 16px;
  transition: font-size 0.25s cubic-bezier(0.5, 0.25, 0, 1);
}

.tile-button {
  all: unset;
}

.tile-menu {
  all: unset;
  min-width: 400px;
  min-height: 300px;
  margin: -999px;
}
.tile-menu.expand {
  margin: 0px;
  transition: margin 0.15s cubic-bezier(0.5, 0.25, 0, 1);
}
.tile-menu.contract {
  margin: -200px;
  transition: margin 0.25s cubic-bezier(0.5, 0.25, 0, 1);
}

.wavy-circle {
  color: #4a90e2; /* primary fill (outer wave) */
  background-color: #2c3e50; /* hour & minute hand */
  border-color: #e74c3c; /* second hand dot */
}
